using GMCadiomLoggerProvider;
using GMCadiomLoggerProvider.Builder;
using GMCadiomLoggerProvider.Factory;
using Microsoft.Extensions.DependencyInjection;
using GMCadiomLoggerProvider.Extensions;

namespace GMCadiomLoggerProvider.Demo
{
    /// <summary>
    /// Demo program showing different ways to use GMCadiom Logger Provider
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== GMCadiom Logger Provider Demo ===\n");

            // Demo 1: Simple Builder Pattern
            Console.WriteLine("1. Simple Builder Pattern Demo:");
            SimpleBuilderDemo();
            Console.WriteLine();

            // Demo 2: Factory Pattern
            Console.WriteLine("2. Factory Pattern Demo:");
            FactoryPatternDemo();
            Console.WriteLine();

            // Demo 3: Dependency Injection
            Console.WriteLine("3. Dependency Injection Demo:");
            DependencyInjectionDemo();
            Console.WriteLine();

            // Demo 4: Provider Comparison
            Console.WriteLine("4. Provider Comparison Demo:");
            ProviderComparisonDemo();
            Console.WriteLine();

            Console.WriteLine("Demo completed! Check the logs folder for file outputs.");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static void SimpleBuilderDemo()
        {
            // Create a logger using the builder pattern
            var logger = new LoggerProviderBuilder()
                .UseSerilog()
                .WithConsoleLogging(true)
                .WithFileLogging(true, "logs/demo-simple-.log")
                .WithMinimumLevel(LogLevel.Information)
                .WithRollingInterval(RollingInterval.Day)
                .Build();

            logger.Information("Simple builder demo started");
            logger.Warning("This is a warning message");
            logger.Error("This is an error message");
            
            try
            {
                throw new InvalidOperationException("Demo exception");
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Caught exception: {Message}", ex.Message);
            }
        }

        static void FactoryPatternDemo()
        {
            var factory = new LoggerProviderFactory();

            // Create options programmatically
            var options = new LoggingOptions
            {
                LoggerProvider = LoggerProvider.Serilog,
                MinimumLevel = LogLevel.Debug,
                EnableConsoleLogging = true,
                EnableFileLogging = true,
                LogFilePath = "logs/demo-factory-.log",
                LevelOverrides = new Dictionary<string, LogLevel>
                {
                    { "Microsoft", LogLevel.Warning },
                    { "System", LogLevel.Error }
                }
            };

            var logger = factory.CreateProvider(options);

            logger.Information("Factory pattern demo started");
            logger.Debug("Debug message from factory-created logger");
            
            // Show available providers
            var providers = factory.GetAvailableProviders();
            logger.Information("Available providers: {Providers}", string.Join(", ", providers));
        }

        static void DependencyInjectionDemo()
        {
            var services = new ServiceCollection();

            // Register the logger with configuration
            services.AddGMCadiomSerilogLogger(options =>
            {
                options.MinimumLevel = LogLevel.Information;
                options.LogFilePath = "logs/demo-di-.log";
                options.EnableConsoleLogging = true;
                options.EnableFileLogging = true;
                options.LevelOverrides["Demo"] = LogLevel.Debug;
            });

            // Register a demo service
            services.AddTransient<DemoService>();

            var serviceProvider = services.BuildServiceProvider();

            // Get the logger and service
            var logger = serviceProvider.GetRequiredService<ILoggingProvider>();
            var demoService = serviceProvider.GetRequiredService<DemoService>();

            logger.Information("Dependency injection demo started");
            demoService.DoWork();
        }

        static void ProviderComparisonDemo()
        {
            Console.WriteLine("Creating Microsoft Logger:");
            var microsoftLogger = new LoggerProviderBuilder()
                .UseMicrosoft()
                .WithConsoleLogging(true)
                .WithMinimumLevel(LogLevel.Information)
                .Build();

            microsoftLogger.Information("Message from Microsoft provider");
            microsoftLogger.Warning("Warning from Microsoft provider");

            Console.WriteLine("\nCreating Serilog Logger:");
            var serilogLogger = new LoggerProviderBuilder()
                .UseSerilog()
                .WithConsoleLogging(true)
                .WithFileLogging(true, "logs/demo-serilog-.log")
                .WithMinimumLevel(LogLevel.Information)
                .Build();

            serilogLogger.Information("Message from Serilog provider");
            serilogLogger.Warning("Warning from Serilog provider");

            Console.WriteLine("\nBoth providers implement the same ILoggingProvider interface!");
        }
    }

    /// <summary>
    /// Demo service to show dependency injection usage
    /// </summary>
    public class DemoService
    {
        private readonly ILoggingProvider _logger;

        public DemoService(ILoggingProvider logger)
        {
            _logger = logger;
        }

        public void DoWork()
        {
            _logger.Information("DemoService.DoWork started");
            
            // Simulate some work
            for (int i = 1; i <= 3; i++)
            {
                _logger.Debug("Processing item {ItemNumber}", i);
                Thread.Sleep(100); // Simulate work
            }

            _logger.Information("DemoService.DoWork completed");
        }
    }
}
