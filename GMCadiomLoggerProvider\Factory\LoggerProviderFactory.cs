using GMCadiomLoggerProvider.Implementation;
using Microsoft.Extensions.Logging;

namespace GMCadiomLoggerProvider.Factory
{
    /// <summary>
    /// Factory implementation for creating logger provider instances
    /// </summary>
    public class LoggerProviderFactory : ILoggerProviderFactory
    {
        private readonly IServiceProvider? _serviceProvider;
        private readonly Dictionary<LoggerProvider, Func<LoggingOptions, ILoggingProvider>> _customProviders;

        public LoggerProviderFactory(IServiceProvider? serviceProvider = null)
        {
            _serviceProvider = serviceProvider;
            _customProviders = new Dictionary<LoggerProvider, Func<LoggingOptions, ILoggingProvider>>();
        }

        public ILoggingProvider CreateProvider(LoggerProvider providerType, LoggingOptions options)
        {
            if (!options.Enabled)
            {
                return new NullLoggingProvider();
            }

            // Check for custom providers first
            if (_customProviders.TryGetValue(providerType, out var customFactory))
            {
                return customFactory(options);
            }

            return providerType switch
            {
                LoggerProvider.Microsoft => CreateMicrosoftProvider(options),
                LoggerProvider.Serilog => CreateSerilogProvider(options),
                _ => throw new NotSupportedException($"Logger provider '{providerType}' is not supported.")
            };
        }

        public ILoggingProvider CreateProvider(LoggingOptions options)
        {
            return CreateProvider(options.LoggerProvider, options);
        }

        public void RegisterCustomProvider(LoggerProvider providerType, Func<LoggingOptions, ILoggingProvider> factory)
        {
            _customProviders[providerType] = factory ?? throw new ArgumentNullException(nameof(factory));
        }

        public IEnumerable<LoggerProvider> GetAvailableProviders()
        {
            var builtInProviders = Enum.GetValues<LoggerProvider>();
            var customProviders = _customProviders.Keys;
            return builtInProviders.Union(customProviders).Distinct();
        }

        private ILoggingProvider CreateMicrosoftProvider(LoggingOptions options)
        {
            if (_serviceProvider != null)
            {
                var logger = _serviceProvider.GetService(typeof(ILogger)) as ILogger;
                if (logger != null)
                {
                    return new MicrosoftLoggingProvider(logger, options);
                }
            }

            // Fallback: create a basic logger factory
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                if (options.EnableConsoleLogging)
                {
                    builder.AddConsole();
                }

                if (options.EnableDebugLogging)
                {
                    builder.AddDebug();
                }

                builder.SetMinimumLevel((Microsoft.Extensions.Logging.LogLevel)options.MinimumLevel);

                foreach (var levelOverride in options.LevelOverrides)
                {
                    builder.AddFilter(levelOverride.Key, (Microsoft.Extensions.Logging.LogLevel)levelOverride.Value);
                }
            });

            var fallbackLogger = loggerFactory.CreateLogger("GMCadiomLogger");
            return new MicrosoftLoggingProvider(fallbackLogger, options);
        }

        private static ILoggingProvider CreateSerilogProvider(LoggingOptions options)
        {
            return new SerilogLoggingProvider(options);
        }
    }

    /// <summary>
    /// Null object pattern implementation for disabled logging
    /// </summary>
    internal class NullLoggingProvider : ILoggingProvider
    {
        public void Debug(string message, params object[] args) { }
        public void Error(string message, params object[] args) { }
        public void Error(Exception exception, string message, params object[] args) { }
        public void Fatal(string message, params object[] args) { }
        public void Information(string message, params object[] args) { }
        public void Log(Configuration.LogLevel level, string message, params object[] args) { }
        public void Trace(string message, params object[] args) { }
        public void Warning(string message, params object[] args) { }
    }
}
