using GMCadiomLoggerProvider.Factory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace GMCadiomLoggerProvider.Extensions
{
    /// <summary>
    /// Extension methods for IServiceCollection to easily configure GMCadiom Logger Provider
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Adds GMCadiom Logger Provider services to the dependency injection container
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configureOptions">Optional action to configure logging options</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddGMCadiomLogger(
            this IServiceCollection services,
            Action<LoggingOptions>? configureOptions = null)
        {
            // Register the factory
            services.TryAddSingleton<ILoggerProviderFactory, LoggerProviderFactory>();

            // Configure options
            var options = new LoggingOptions();
            configureOptions?.Invoke(options);
            services.TryAddSingleton(options);

            // Register the logging provider
            services.TryAddSingleton<ILoggingProvider>(serviceProvider =>
            {
                var factory = serviceProvider.GetRequiredService<ILoggerProviderFactory>();
                var loggingOptions = serviceProvider.GetRequiredService<LoggingOptions>();
                return factory.CreateProvider(loggingOptions);
            });

            return services;
        }

        /// <summary>
        /// Adds GMCadiom Logger Provider services with specific provider type
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="providerType">The logger provider type to use</param>
        /// <param name="configureOptions">Optional action to configure logging options</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddGMCadiomLogger(
            this IServiceCollection services,
            LoggerProvider providerType,
            Action<LoggingOptions>? configureOptions = null)
        {
            return services.AddGMCadiomLogger(options =>
            {
                options.LoggerProvider = providerType;
                configureOptions?.Invoke(options);
            });
        }

        /// <summary>
        /// Adds GMCadiom Logger Provider services with Microsoft Logging
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configureOptions">Optional action to configure logging options</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddGMCadiomMicrosoftLogger(
            this IServiceCollection services,
            Action<LoggingOptions>? configureOptions = null)
        {
            return services.AddGMCadiomLogger(LoggerProvider.Microsoft, configureOptions);
        }

        /// <summary>
        /// Adds GMCadiom Logger Provider services with Serilog
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configureOptions">Optional action to configure logging options</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddGMCadiomSerilogLogger(
            this IServiceCollection services,
            Action<LoggingOptions>? configureOptions = null)
        {
            return services.AddGMCadiomLogger(LoggerProvider.Serilog, configureOptions);
        }

        /// <summary>
        /// Registers a custom logger provider
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="providerType">The provider type to register</param>
        /// <param name="factory">Factory function to create the provider</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddCustomLoggerProvider(
            this IServiceCollection services,
            LoggerProvider providerType,
            Func<LoggingOptions, ILoggingProvider> factory)
        {
            services.PostConfigure<ILoggerProviderFactory>(factoryInstance =>
            {
                if (factoryInstance is LoggerProviderFactory concreteFactory)
                {
                    concreteFactory.RegisterCustomProvider(providerType, factory);
                }
            });

            return services;
        }
    }
}
