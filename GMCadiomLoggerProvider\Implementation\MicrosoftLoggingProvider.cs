﻿using Microsoft.Extensions.Logging;

namespace GMCadiomLoggerProvider.Implementation
{
    internal class MicrosoftLoggingProvider : ILoggingProvider
    {
        private readonly ILogger _logger;
        private readonly LoggingOptions _options;

        public MicrosoftLoggingProvider(ILogger logger, IOptions<LoggingOptions> options)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        }

        public MicrosoftLoggingProvider(ILogger logger, LoggingOptions options)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options ?? throw new ArgumentNullException(nameof(options));
        }

        public void Debug(string message, params object[] args) =>
            _logger.LogDebug(message, args);

        public void Error(string message, params object[] args) =>
            _logger.LogError(message, args);

        public void Error(Exception exception, string message, params object[] args) =>
            _logger.LogError(exception, message, args);

        public void Fatal(string message, params object[] args) =>
            _logger.LogCritical(message, args);

        public void Information(string message, params object[] args) =>
            _logger.LogInformation(message, args);

        public void Log(Configuration.LogLevel level, string message, params object[] args) =>
            _logger.Log((Microsoft.Extensions.Logging.LogLevel)level, message, args);

        public void Trace(string message, params object[] args) =>
            _logger.LogTrace(message, args);

        public void Warning(string message, params object[] args) =>
            _logger.LogWarning(message, args);
    }
}
