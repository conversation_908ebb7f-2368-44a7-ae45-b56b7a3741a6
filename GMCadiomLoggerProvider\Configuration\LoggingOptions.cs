﻿namespace GMCadiomLoggerProvider.Configuration
{
    public class LoggingOptions
    {
        public bool Enabled { get; set; } = true;
        public LoggerProvider LoggerProvider = LoggerProvider.Serilog;
        public LogLevel MinimumLevel { get; set; } = LogLevel.Information;
        public string? LogFilePath { get; set; }
        public bool EnableConsoleLogging { get; set; } = true;
        public bool EnableFileLogging { get; set; } = true;
        public bool EnableDebugLogging { get; set; } = true;
        public RollingInterval RollingInterval { get; set; } = RollingInterval.Day;
        public Dictionary<string, LogLevel> LevelOverrides { get; set; } = new()
        {
            { "Microsoft", LogLevel.Warning },
            { "System", LogLevel.Warning }
        };
        public StructuredLoggingOptions StructuredLogging { get; set; } = new();
    }
}
