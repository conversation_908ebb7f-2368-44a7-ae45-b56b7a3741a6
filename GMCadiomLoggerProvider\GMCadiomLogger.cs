namespace GMCadiomLoggerProvider
{
    /// <summary>
    /// Static helper class for quick and easy logger creation
    /// </summary>
    public static class GMCadiomLogger
    {
        /// <summary>
        /// Creates a new logger builder for fluent configuration
        /// </summary>
        /// <returns>A new LoggerProviderBuilder instance</returns>
        public static LoggerProviderBuilder Create()
        {
            return new LoggerProviderBuilder();
        }

        /// <summary>
        /// Creates a logger with default Serilog configuration
        /// </summary>
        /// <returns>A configured ILoggingProvider instance</returns>
        public static ILoggingProvider CreateDefault()
        {
            return new LoggerProviderBuilder()
                .UseSerilog()
                .WithConsoleLogging(true)
                .WithMinimumLevel(LogLevel.Information)
                .Build();
        }

        /// <summary>
        /// Creates a logger with default Microsoft Logging configuration
        /// </summary>
        /// <returns>A configured ILoggingProvider instance</returns>
        public static ILoggingProvider CreateMicrosoft()
        {
            return new LoggerProviderBuilder()
                .UseMicrosoft()
                .WithConsoleLogging(true)
                .WithDebugLogging(true)
                .WithMinimumLevel(LogLevel.Information)
                .Build();
        }

        /// <summary>
        /// Creates a logger with default Serilog configuration
        /// </summary>
        /// <returns>A configured ILoggingProvider instance</returns>
        public static ILoggingProvider CreateSerilog()
        {
            return new LoggerProviderBuilder()
                .UseSerilog()
                .WithConsoleLogging(true)
                .WithMinimumLevel(LogLevel.Information)
                .Build();
        }

        /// <summary>
        /// Creates a file logger with Serilog
        /// </summary>
        /// <param name="filePath">Path for the log file</param>
        /// <param name="minimumLevel">Minimum log level (default: Information)</param>
        /// <returns>A configured ILoggingProvider instance</returns>
        public static ILoggingProvider CreateFileLogger(string filePath, LogLevel minimumLevel = LogLevel.Information)
        {
            return new LoggerProviderBuilder()
                .UseSerilog()
                .WithConsoleLogging(true)
                .WithFileLogging(true, filePath)
                .WithMinimumLevel(minimumLevel)
                .WithRollingInterval(RollingInterval.Day)
                .Build();
        }

        /// <summary>
        /// Creates a console-only logger
        /// </summary>
        /// <param name="providerType">The provider type to use (default: Serilog)</param>
        /// <param name="minimumLevel">Minimum log level (default: Information)</param>
        /// <returns>A configured ILoggingProvider instance</returns>
        public static ILoggingProvider CreateConsoleLogger(
            LoggerProvider providerType = LoggerProvider.Serilog, 
            LogLevel minimumLevel = LogLevel.Information)
        {
            return new LoggerProviderBuilder()
                .UseProvider(providerType)
                .WithConsoleLogging(true)
                .WithFileLogging(false)
                .WithMinimumLevel(minimumLevel)
                .Build();
        }

        /// <summary>
        /// Creates a production-ready logger with file and console output
        /// </summary>
        /// <param name="applicationName">Name of the application for log file naming</param>
        /// <param name="logDirectory">Directory for log files (default: "logs")</param>
        /// <returns>A configured ILoggingProvider instance</returns>
        public static ILoggingProvider CreateProductionLogger(string applicationName, string logDirectory = "logs")
        {
            var logPath = Path.Combine(logDirectory, $"{applicationName}-.log");
            
            return new LoggerProviderBuilder()
                .UseSerilog()
                .WithConsoleLogging(true)
                .WithFileLogging(true, logPath)
                .WithMinimumLevel(LogLevel.Information)
                .WithRollingInterval(RollingInterval.Day)
                .WithLevelOverride("Microsoft", LogLevel.Warning)
                .WithLevelOverride("System", LogLevel.Warning)
                .WithStructuredLogging(structured =>
                {
                    structured.IncludeTimestamp = true;
                    structured.IncludeRequestId = true;
                })
                .Build();
        }

        /// <summary>
        /// Creates a development logger with verbose output
        /// </summary>
        /// <param name="applicationName">Name of the application for log file naming</param>
        /// <returns>A configured ILoggingProvider instance</returns>
        public static ILoggingProvider CreateDevelopmentLogger(string applicationName)
        {
            var logPath = Path.Combine("logs", $"{applicationName}-dev-.log");
            
            return new LoggerProviderBuilder()
                .UseSerilog()
                .WithConsoleLogging(true)
                .WithFileLogging(true, logPath)
                .WithDebugLogging(true)
                .WithMinimumLevel(LogLevel.Debug)
                .WithRollingInterval(RollingInterval.Hour)
                .WithStructuredLogging(structured =>
                {
                    structured.IncludeTimestamp = true;
                    structured.IncludeRequestId = true;
                    structured.IncludeUserId = true;
                })
                .Build();
        }

        /// <summary>
        /// Creates a disabled logger (null object pattern)
        /// </summary>
        /// <returns>A disabled ILoggingProvider instance</returns>
        public static ILoggingProvider CreateDisabled()
        {
            return new LoggerProviderBuilder()
                .WithEnabled(false)
                .Build();
        }
    }
}
