using Microsoft.Extensions.DependencyInjection;

namespace GMCadiomLoggerProvider.Examples
{
    /// <summary>
    /// Examples demonstrating how to use GMCadiom Logger Provider
    /// </summary>
    public static class UsageExamples
    {
        /// <summary>
        /// Example 1: Basic usage with builder pattern
        /// </summary>
        public static void BasicBuilderExample()
        {
            // Create logger with Serilog provider
            var logger = new LoggerProviderBuilder()
                .UseSerilog()
                .WithConsoleLogging()
                .WithFileLogging(true, "logs/example-.log")
                .WithMinimumLevel(LogLevel.Information)
                .Build();

            // Use the logger
            logger.Information("Application started");
            logger.Debug("This won't be logged due to minimum level");
            logger.Warning("This is a warning message");
            logger.Error("This is an error message");

            try
            {
                throw new InvalidOperationException("Example exception");
            }
            catch (Exception ex)
            {
                logger.Error(ex, "An error occurred: {ErrorMessage}", ex.Message);
            }
        }

        /// <summary>
        /// Example 2: Using Microsoft Logging provider
        /// </summary>
        public static void MicrosoftLoggingExample()
        {
            var logger = new LoggerProviderBuilder()
                .UseMicrosoft()
                .WithConsoleLogging()
                .WithDebugLogging()
                .WithMinimumLevel(LogLevel.Debug)
                .Build();

            logger.Debug("Debug message from Microsoft provider");
            logger.Information("Information message");
            logger.Warning("Warning message");
        }

        /// <summary>
        /// Example 3: Dependency injection setup
        /// </summary>
        public static void DependencyInjectionExample()
        {
            var services = new ServiceCollection();

            // Register GMCadiom Logger with Serilog
            services.AddGMCadiomSerilogLogger(options =>
            {
                options.MinimumLevel = LogLevel.Information;
                options.LogFilePath = "logs/di-example-.log";
                options.EnableConsoleLogging = true;
                options.EnableFileLogging = true;
                options.RollingInterval = RollingInterval.Day;
                
                // Reduce noise from Microsoft and System namespaces
                options.LevelOverrides["Microsoft"] = LogLevel.Warning;
                options.LevelOverrides["System"] = LogLevel.Warning;
                
                // Configure structured logging
                options.StructuredLogging.IncludeTimestamp = true;
                options.StructuredLogging.IncludeRequestId = true;
            });

            // Register a service that uses the logger
            services.AddTransient<ExampleService>();

            var serviceProvider = services.BuildServiceProvider();
            var exampleService = serviceProvider.GetRequiredService<ExampleService>();
            
            exampleService.DoWork();
        }

        /// <summary>
        /// Example 4: Advanced configuration with multiple overrides
        /// </summary>
        public static void AdvancedConfigurationExample()
        {
            var logger = new LoggerProviderBuilder()
                .UseSerilog()
                .WithMinimumLevel(LogLevel.Debug)
                .WithConsoleLogging(true)
                .WithFileLogging(true, "logs/advanced-.log")
                .WithRollingInterval(RollingInterval.Hour)
                .WithLevelOverride("Microsoft", LogLevel.Warning)
                .WithLevelOverride("System", LogLevel.Error)
                .WithLevelOverride("MyApp.Database", LogLevel.Debug)
                .WithStructuredLogging(structured =>
                {
                    structured.IncludeTimestamp = true;
                    structured.IncludeUserId = true;
                    structured.IncludeConnectionId = true;
                    structured.IncludeRequestId = true;
                })
                .Build();

            logger.Information("Advanced configuration example");
            logger.Debug("Debug message with structured logging");
        }

        /// <summary>
        /// Example 5: Factory pattern usage
        /// </summary>
        public static void FactoryPatternExample()
        {
            var factory = new LoggerProviderFactory();

            // Create options
            var options = new LoggingOptions
            {
                LoggerProvider = LoggerProvider.Serilog,
                MinimumLevel = LogLevel.Information,
                EnableConsoleLogging = true,
                EnableFileLogging = true,
                LogFilePath = "logs/factory-.log"
            };

            // Create provider using factory
            var logger = factory.CreateProvider(options);

            logger.Information("Logger created using factory pattern");

            // Get available providers
            var availableProviders = factory.GetAvailableProviders();
            logger.Information("Available providers: {Providers}", 
                string.Join(", ", availableProviders));
        }

        /// <summary>
        /// Example 6: Conditional logging based on configuration
        /// </summary>
        public static void ConditionalLoggingExample()
        {
            // Disabled logger example
            var disabledLogger = new LoggerProviderBuilder()
                .UseSerilog()
                .WithEnabled(false)
                .Build();

            // These won't be logged
            disabledLogger.Information("This won't be logged");
            disabledLogger.Error("Neither will this");

            // Enabled logger
            var enabledLogger = new LoggerProviderBuilder()
                .UseSerilog()
                .WithEnabled(true)
                .WithConsoleLogging()
                .Build();

            enabledLogger.Information("This will be logged");
        }
    }

    /// <summary>
    /// Example service that demonstrates logger usage in a class
    /// </summary>
    public class ExampleService
    {
        private readonly ILoggingProvider _logger;

        public ExampleService(ILoggingProvider logger)
        {
            _logger = logger;
        }

        public void DoWork()
        {
            _logger.Information("ExampleService.DoWork started");

            try
            {
                // Simulate some work
                _logger.Debug("Processing data...");
                
                // Simulate potential warning
                _logger.Warning("Low memory warning");
                
                _logger.Information("Work completed successfully");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error occurred in DoWork");
                throw;
            }
        }
    }
}
